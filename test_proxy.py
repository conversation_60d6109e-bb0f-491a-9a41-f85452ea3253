#!/usr/bin/env python3
"""
Test script to verify proxy rotation and Vercel bypass functionality
"""

from curl_cffi import requests
import random
import time

# Same proxy list from test.py
PROXY_LIST = [
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@eclat-au.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@kidney-au.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@eonism-au.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@uredo-au.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@descrive-au.netwpool.com:10799",
]

current_proxy_index = 0

def get_next_proxy():
    """Get the next proxy from the rotation list"""
    global current_proxy_index
    if not PROXY_LIST:
        return None
    
    proxy = PROXY_LIST[current_proxy_index]
    current_proxy_index = (current_proxy_index + 1) % len(PROXY_LIST)
    return proxy

def get_enhanced_headers():
    """Get enhanced headers to bypass Vercel protection"""
    return {
        'accept': '*/*',
        'accept-language': 'en-US,en;q=0.9,en-GB;q=0.8',
        'cache-control': 'no-cache',
        'content-type': 'application/json;charset=UTF-8',
        'origin': 'https://beta.lmarena.ai',
        'pragma': 'no-cache',
        'priority': 'u=1, i',
        'referer': 'https://beta.lmarena.ai/',
        'sec-ch-ua': '"Google Chrome";v="131", "Chromium";v="131", "Not_A Brand";v="24"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-platform': '"Windows"',
        'sec-fetch-dest': 'empty',
        'sec-fetch-mode': 'cors',
        'sec-fetch-site': 'same-origin',
        'user-agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
        'x-forwarded-for': f"{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}",
        'x-real-ip': f"{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}.{random.randint(1,255)}",
    }

def test_proxy_access():
    """Test accessing LM Arena with proxy rotation"""
    print("🧪 Testing Proxy Access to LM Arena")
    print("=" * 50)
    
    test_urls = [
        'https://beta.lmarena.ai/',
        'https://alpha.lmarena.ai/',
    ]
    
    for url in test_urls:
        print(f"\n🔗 Testing: {url}")
        
        for attempt in range(3):
            proxy = get_next_proxy()
            print(f"🔄 Attempt {attempt + 1}/3 with proxy: {proxy.split('@')[-1]}")
            
            try:
                response = requests.get(
                    url,
                    headers=get_enhanced_headers(),
                    proxies={'http': proxy, 'https': proxy} if proxy else None,
                    impersonate="chrome110",
                    timeout=15,
                    allow_redirects=False
                )
                
                print(f"📊 Status: {response.status_code}")
                
                if "Vercel Security Checkpoint" in response.text:
                    print("🚫 Vercel checkpoint detected")
                elif response.status_code == 200:
                    print("✅ Success! No Vercel blocking detected")
                    break
                elif response.status_code == 403:
                    print("🚫 403 Forbidden - likely blocked")
                else:
                    print(f"ℹ️ Unexpected status: {response.status_code}")
                    
            except Exception as e:
                print(f"❌ Error: {e}")
            
            if attempt < 2:
                delay = random.uniform(1, 3)
                print(f"⏳ Waiting {delay:.1f}s before retry...")
                time.sleep(delay)

if __name__ == "__main__":
    test_proxy_access()
