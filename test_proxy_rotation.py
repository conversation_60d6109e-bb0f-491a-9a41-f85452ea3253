#!/usr/bin/env python3
"""
Test script for proxy rotation functionality
"""

import requests
import json
import time

def test_proxy_status():
    """Test the proxy status endpoint"""
    try:
        response = requests.get('http://localhost:32938/proxy-status')
        print(f"Proxy Status Response: {response.status_code}")
        print(json.dumps(response.json(), indent=2))
        return response.json()
    except Exception as e:
        print(f"Error checking proxy status: {e}")
        return None

def test_chat_completion():
    """Test a simple chat completion to trigger proxy usage"""
    try:
        payload = {
            "messages": [
                {"role": "user", "content": "Hello, how are you?"}
            ],
            "stream": False
        }
        
        print("Sending chat completion request...")
        response = requests.post(
            'http://localhost:32938/v1/chat/completions',
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        
        print(f"Chat Completion Response: {response.status_code}")
        if response.status_code == 200:
            result = response.json()
            print(f"Response content: {result.get('choices', [{}])[0].get('message', {}).get('content', 'No content')[:100]}...")
        else:
            print(f"Error response: {response.text}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"Error in chat completion: {e}")
        return False

def test_multiple_requests():
    """Test multiple requests to see proxy rotation in action"""
    print("\n" + "="*50)
    print("Testing multiple requests for proxy rotation...")
    print("="*50)
    
    success_count = 0
    total_requests = 5
    
    for i in range(total_requests):
        print(f"\nRequest {i+1}/{total_requests}:")
        success = test_chat_completion()
        if success:
            success_count += 1
        
        # Check proxy status after each request
        status = test_proxy_status()
        if status and status.get('proxy_enabled'):
            print(f"Working proxies: {status.get('working_proxies')}/{status.get('total_proxies')}")
        
        time.sleep(2)  # Small delay between requests
    
    print(f"\nSummary: {success_count}/{total_requests} requests successful")

def reset_proxies():
    """Reset the failed proxy list"""
    try:
        response = requests.post('http://localhost:32938/reset-proxies')
        print(f"Reset Proxies Response: {response.status_code}")
        print(response.json())
    except Exception as e:
        print(f"Error resetting proxies: {e}")

if __name__ == "__main__":
    print("Proxy Rotation Test Script")
    print("="*30)
    
    # First check if server is running
    try:
        response = requests.get('http://localhost:32938/proxy-status', timeout=5)
        print("✅ Server is running")
    except:
        print("❌ Server is not running. Please start test.py first.")
        exit(1)
    
    # Test initial proxy status
    print("\n1. Initial proxy status:")
    test_proxy_status()
    
    # Test multiple requests
    test_multiple_requests()
    
    # Final proxy status
    print("\n2. Final proxy status:")
    test_proxy_status()
    
    # Option to reset proxies
    user_input = input("\nDo you want to reset failed proxies? (y/n): ")
    if user_input.lower() == 'y':
        reset_proxies()
        print("\nProxy status after reset:")
        test_proxy_status()
