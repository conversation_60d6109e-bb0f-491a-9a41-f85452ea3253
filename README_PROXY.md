# Proxy Rotation System for LM Arena

This enhanced version of the LM Arena proxy includes automatic proxy rotation to avoid getting flagged by Vercel and Cloudflare.

## Features

- **Automatic Proxy Rotation**: Cycles through multiple proxy servers automatically
- **Failure Detection**: Automatically detects and marks failed proxies
- **Fallback Support**: Falls back to direct connection if all proxies fail
- **Health Monitoring**: Real-time monitoring of proxy status
- **Easy Configuration**: Simple configuration file for managing proxies
- **Thread-Safe**: Safe for concurrent requests

## Setup

1. **Install Dependencies**:
   ```bash
   pip install flask requests
   ```

2. **Configure Proxies**:
   Edit `proxy_config.py` to customize your proxy settings:
   ```python
   USE_PROXIES = True  # Enable/disable proxy rotation
   MAX_PROXY_ATTEMPTS = 3  # Max attempts per request
   PROXY_TIMEOUT = 30  # Timeout per proxy attempt
   ```

3. **Add Your Proxies**:
   Update the `PROXY_LIST` in `proxy_config.py` with your proxy URLs.

## Usage

### Starting the Server

```bash
python test.py
```

The server will start and display:
- Proxy rotation status
- Number of configured proxies
- Available endpoints

### API Endpoints

#### Chat Completions (OpenAI Compatible)
```bash
POST /v1/chat/completions
```

#### Proxy Status Monitoring
```bash
GET /proxy-status
```
Returns:
```json
{
  "proxy_enabled": true,
  "total_proxies": 14,
  "working_proxies": 12,
  "failed_proxies": 2,
  "failed_proxy_list": ["proxy1.com:10799", "proxy2.com:10799"]
}
```

#### Reset Failed Proxies
```bash
POST /reset-proxies
```

### Testing

Run the test script to verify proxy rotation:
```bash
python test_proxy_rotation.py
```

This will:
- Check proxy status
- Send multiple requests to test rotation
- Show which proxies are working/failed
- Allow you to reset failed proxies

## How It Works

1. **Request Processing**: Each API request attempts to use a proxy
2. **Proxy Selection**: The system rotates through available proxies
3. **Failure Detection**: Failed proxies are marked and skipped
4. **Automatic Retry**: If a proxy fails, the next one is tried automatically
5. **Fallback**: If all proxies fail, requests continue without proxy

## Configuration Options

### Proxy Settings
- `USE_PROXIES`: Enable/disable proxy rotation
- `MAX_PROXY_ATTEMPTS`: Maximum proxy attempts per request
- `PROXY_TIMEOUT`: Timeout for each proxy request
- `PROXY_LIST`: List of proxy URLs with authentication

### Failure Detection
- `PROXY_FAILURE_STATUS_CODES`: HTTP status codes indicating proxy failure
- `PROXY_FAILURE_KEYWORDS`: Keywords in error messages indicating proxy issues

## Proxy URL Format

Proxies should be in the format:
```
******************************:port
```

Example:
```
https://user123:<EMAIL>:10799
```

## Monitoring and Maintenance

### Check Proxy Health
Visit `http://localhost:32938/proxy-status` to see:
- Total number of proxies
- Working vs failed proxies
- List of failed proxy endpoints

### Reset Failed Proxies
If proxies come back online, reset the failed list:
```bash
curl -X POST http://localhost:32938/reset-proxies
```

### Logs
The server logs show:
- Which proxy is being used for each request
- Proxy failures and reasons
- Automatic failover events

## Troubleshooting

### All Proxies Failing
1. Check proxy credentials and URLs
2. Verify proxy server status
3. Check network connectivity
4. Reset failed proxy list

### Slow Responses
1. Reduce `PROXY_TIMEOUT` for faster failover
2. Remove consistently slow proxies
3. Increase `MAX_PROXY_ATTEMPTS` if needed

### No Proxy Rotation
1. Ensure `USE_PROXIES = True` in config
2. Check that `PROXY_LIST` is not empty
3. Verify proxy URL format

## Security Notes

- Proxy credentials are logged only partially (host:port)
- Full URLs with credentials are not displayed in logs
- Consider using environment variables for sensitive proxy credentials

## Performance Tips

- Use geographically diverse proxies for better performance
- Monitor proxy response times and remove slow ones
- Adjust timeout values based on your proxy performance
- Use the reset endpoint to clear failed proxies periodically
