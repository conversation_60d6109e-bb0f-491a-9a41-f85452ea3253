"""
Proxy configuration file for easy management
"""

# Enable/disable proxy rotation
USE_PROXIES = True

# Maximum number of proxy attempts per request
MAX_PROXY_ATTEMPTS = 3

# Timeout for each proxy request (seconds)
PROXY_TIMEOUT = 30

# List of proxy URLs with authentication
PROXY_LIST = [
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@eclat-au.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@kidney-au.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@eonism-au.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@uredo-au.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@descrive-au.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@larur-ar.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@utopian-by.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@outgoing-by.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@incredible-by.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@didapper-be.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@wasteful-br.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@natation-bg.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@gemsbok-ca.netwpool.com:10799",
    "https://6zxgk3bvbzf8ex2pqnyu7gvsmqfk7xhb:<EMAIL>@kurta-ca.netwpool.com:10799"
]

# HTTP status codes that indicate proxy-related failures
PROXY_FAILURE_STATUS_CODES = [403, 429, 502, 503, 504]

# Keywords in error messages that indicate proxy failures
PROXY_FAILURE_KEYWORDS = ['proxy', 'connection', 'timeout', 'refused', 'unreachable']

# LM Arena configuration
DEFAULT_LM_ARENA_MODEL_ID = "gpt-4o-3214"
SESSIONS = ["27b83a8d-a926-4232-b60f-1f941ce264b9"]

# Server configuration
SERVER_HOST = "0.0.0.0"
SERVER_PORT = 32938
